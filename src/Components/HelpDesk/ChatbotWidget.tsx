import React, { useState, useRef, useEffect } from "react";

interface Message {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  options?: string[];
}

interface ChatbotWidgetProps {
  className?: string;
}

const ChatbotWidget: React.FC<ChatbotWidgetProps> = ({ className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    // Auto-hide welcome tooltip after 5 seconds
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const predefinedResponses: {
    [key: string]: { text: string; options?: string[] };
  } = {
    "technical support": {
      text: "I can help you with technical issues. What specific problem are you experiencing?",
      options: [
        "Login Issues",
        "Platform Errors",
        "Performance Problems",
        "Feature Questions",
      ],
    },
    "account questions": {
      text: "I'm here to help with account-related questions. What would you like to know?",
      options: [
        "Profile Settings",
        "Subscription",
        "Billing",
        "Account Security",
      ],
    },
    "general information": {
      text: "I can provide general information about GTI. What would you like to learn about?",
      options: [
        "About GTI",
        "Services",
        "Getting Started",
        "Contact Information",
      ],
    },
    "contact support": {
      text: "You can reach our support <NAME_EMAIL> or use the contact form on this page. Is there anything specific I can help you with right now?",
    },
    "login issues": {
      text: "For login issues, please try:\n• Clear your browser cache and cookies\n• Reset your password using the 'Forgot Password' link\n• Check your internet connection\n• Try using a different browser\n\nIf the problem persists, contact our support <NAME_EMAIL>",
    },
    "platform errors": {
      text: "For platform errors, please:\n• Refresh the page and try again\n• Check if you're using a supported browser\n• Disable browser extensions temporarily\n• Clear your browser cache\n\nIf the error continues, please contact support with details about the error message.",
    },
    "performance problems": {
      text: "To improve platform performance:\n• Check your internet connection speed\n• Close unnecessary browser tabs\n• Clear browser cache and cookies\n• Try using a different browser\n• Ensure your browser is up to date\n\nFor persistent issues, contact our technical team.",
    },
    "about gti": {
      text: "GTI (Global Technology Internationalisation) is a platform that connects technology innovators worldwide. We facilitate international collaboration, business opportunities, and knowledge sharing in the tech sector.\n\nKey features:\n• Technology showcase and discovery\n• International business connections\n• Innovation opportunities\n• Expert support and guidance",
    },
    services: {
      text: "GTI offers comprehensive services including:\n• Technology Internationalisation Support\n• Business Matching and Networking\n• Market Entry Assistance\n• Innovation Consulting\n• Premium Services for Enhanced Visibility\n\nWould you like to know more about any specific service?",
    },
    "getting started": {
      text: "Getting started with GTI is easy:\n1. Create your account and complete your profile\n2. Explore technologies and opportunities\n3. Connect with relevant partners\n4. Utilize our helpdesk for support\n\nNeed help with any of these steps?",
    },
    "profile settings": {
      text: "To manage your profile settings:\n• Go to Profile → Personal/Company sections\n• Update your information and preferences\n• Add your technologies and expertise\n• Set your visibility preferences\n\nNeed specific help with profile setup?",
    },
    hello: {
      text: "Hello! Welcome to GTI Helpdesk. I'm here to assist you with any questions about our platform, technical issues, or general information. How can I help you today?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    hi: {
      text: "Hi there! I'm your GTI assistant. I can help you with technical support, account questions, or provide information about our services. What would you like to know?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
    help: {
      text: "I'm here to help! I can assist you with:\n• Technical issues and troubleshooting\n• Account and profile questions\n• Information about GTI services\n• Connecting you with our support team\n\nWhat do you need help with?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    default: {
      text: "I understand you're looking for help. Let me connect you with the right resources. You can also contact our support team <NAME_EMAIL>",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
  };

  const generateBotResponse = (
    userMessage: string
  ): { text: string; options?: string[] } => {
    const lowerMessage = userMessage.toLowerCase().trim();

    // Direct keyword matches
    const keywordMatches = [
      { keywords: ["hello", "hi", "hey", "greetings"], response: "hello" },
      { keywords: ["help", "assist", "support"], response: "help" },
      {
        keywords: ["technical", "tech", "bug", "error", "problem", "issue"],
        response: "technical support",
      },
      {
        keywords: ["account", "profile", "user", "settings"],
        response: "account questions",
      },
      {
        keywords: ["information", "info", "about", "what is"],
        response: "general information",
      },
      {
        keywords: ["contact", "reach", "email", "phone"],
        response: "contact support",
      },
      {
        keywords: ["login", "sign in", "password", "authentication"],
        response: "login issues",
      },
      {
        keywords: ["slow", "performance", "speed", "loading"],
        response: "performance problems",
      },
      { keywords: ["gti", "platform", "company"], response: "about gti" },
      { keywords: ["service", "offer", "feature"], response: "services" },
      {
        keywords: ["start", "begin", "new", "getting started"],
        response: "getting started",
      },
      {
        keywords: ["profile", "setting", "update"],
        response: "profile settings",
      },
    ];

    // Check for keyword matches
    for (const match of keywordMatches) {
      if (match.keywords.some((keyword) => lowerMessage.includes(keyword))) {
        return predefinedResponses[match.response];
      }
    }

    // Check for exact phrase matches
    for (const [key, response] of Object.entries(predefinedResponses)) {
      if (key !== "default" && lowerMessage.includes(key.toLowerCase())) {
        return response;
      }
    }

    return predefinedResponses.default;
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isBot: false,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsTyping(true);

    // Simulate bot typing delay
    setTimeout(() => {
      const botResponse = generateBotResponse(text);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: botResponse.text,
        isBot: true,
        timestamp: new Date(),
        options: botResponse.options,
      };

      setMessages((prev) => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds
  };

  const handleOptionClick = (option: string) => {
    handleSendMessage(option);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(inputValue);
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setShowWelcome(false);
    }
  };

  return (
    <div
      className={`fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-[9999] ${className}`}
    >
      {/* Chat Window - DataStax Style */}
      {isOpen && (
        <div className="mb-4 w-96 max-w-[calc(100vw-2rem)] h-[36rem] max-h-[calc(100vh-8rem)] bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 rounded-3xl shadow-2xl flex flex-col animate-fade-in-up overflow-hidden">
          {/* Header */}
          <div className="p-6 pb-4 relative">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="text-white font-bold text-lg tracking-wider">
                  GTI
                </div>
                <div className="w-8 h-8 bg-white/20 rounded-full overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                    <span className="text-white text-xs font-semibold">G</span>
                  </div>
                </div>
              </div>
              <button
                onClick={toggleChat}
                className="text-white/70 hover:text-white transition-colors w-6 h-6 flex items-center justify-center"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Welcome Message */}
            <div className="mt-6">
              <h2 className="text-white text-xl font-semibold mb-2">
                Hi Code 👋
              </h2>
              <p className="text-white/90 text-base">How can we help?</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 px-6 pb-6 space-y-4">
            {/* Ask a Question Card */}
            <button
              onClick={() => handleSendMessage("I need help with something")}
              className="w-full bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-lg hover:bg-white transition-all duration-200 hover:shadow-xl"
            >
              <div className="flex items-center justify-between">
                <div className="text-left">
                  <h3 className="font-semibold text-gray-800 text-sm">
                    Ask a question
                  </h3>
                  <p className="text-gray-600 text-xs mt-1">
                    AI Agent and team can help
                  </p>
                </div>
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-purple-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </button>

            {/* Featured Content Card */}
            <div className="bg-gradient-to-r from-GTI-BLUE-default to-purple-700 rounded-2xl p-4 text-white relative overflow-hidden">
              <div className="relative z-10">
                <div className="text-xs text-blue-200 font-medium mb-2">
                  UPCOMING WEBINAR
                </div>
                <h3 className="font-bold text-sm mb-2 leading-tight">
                  Global Technology Internationalisation: Expanding Your Tech
                  Business
                </h3>
                <div className="text-xs text-blue-100">
                  with GTI Expert Team
                </div>
              </div>
              <div className="absolute right-2 top-2 w-12 h-12 bg-blue-200 rounded-full overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-blue-300 to-blue-500 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">GTI</span>
                </div>
              </div>
            </div>

            {/* Support Card */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-lg">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="text-xs text-purple-600 font-medium mb-1">
                    Support
                  </div>
                  <h3 className="font-semibold text-gray-800 text-sm mb-1 leading-tight">
                    Need help with your GTI account or platform features?
                  </h3>
                  <p className="text-gray-600 text-xs">
                    Contact our support team | Available 24/7
                  </p>
                </div>
                <svg
                  className="w-4 h-4 text-gray-400 mt-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </div>

            {/* Messages Area (if any) */}
            {messages.length > 0 && (
              <div className="space-y-3">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.isBot ? "justify-start" : "justify-end"
                    }`}
                  >
                    <div
                      className={`max-w-xs px-4 py-3 rounded-2xl ${
                        message.isBot
                          ? "bg-white/95 text-gray-800"
                          : "bg-white/20 text-white"
                      }`}
                    >
                      <div className="text-sm whitespace-pre-line">
                        {message.text}
                      </div>
                      {message.options && (
                        <div className="mt-3 space-y-2">
                          {message.options.map((option, index) => (
                            <button
                              key={index}
                              onClick={() => handleOptionClick(option)}
                              className="block w-full text-left px-3 py-2 text-xs bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200 text-gray-700 font-medium"
                            >
                              {option}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-white/95 px-4 py-2 rounded-2xl">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Bottom Navigation */}
          <div className="bg-white/95 backdrop-blur-sm rounded-b-3xl p-4">
            <div className="flex items-center justify-around">
              <button className="flex flex-col items-center space-y-1 text-purple-600">
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                </svg>
                <span className="text-xs font-medium">Home</span>
              </button>

              <button className="flex flex-col items-center space-y-1 text-gray-400">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
                <span className="text-xs">Messages</span>
              </button>

              <button className="flex flex-col items-center space-y-1 text-gray-400">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 17h5l-5 5v-5zM12 17H7a2 2 0 01-2-2V5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2z"
                  />
                </svg>
                <span className="text-xs">News</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Chat Toggle Button */}
      <button
        onClick={toggleChat}
        className="w-14 h-14 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center group relative"
      >
        {isOpen ? (
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ) : (
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </button>

      {/* Welcome Tooltip */}
      {showWelcome && !isOpen && (
        <div className="absolute bottom-16 right-0 bg-white rounded-lg shadow-lg p-3 max-w-xs border border-gray-200 animate-fade-in-up">
          <div className="text-sm text-gray-800 font-medium">
            👋 Need help? I'm here to assist you!
          </div>
          <div className="absolute bottom-0 right-4 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200"></div>
          <button
            onClick={() => setShowWelcome(false)}
            className="absolute top-1 right-1 text-gray-400 hover:text-gray-600"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default ChatbotWidget;
