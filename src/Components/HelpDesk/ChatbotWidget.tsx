import React, { useState, useRef, useEffect } from "react";

interface Message {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  options?: string[];
}

interface ChatbotWidgetProps {
  className?: string;
}

type ChatView = "home" | "messages" | "news" | "chat";

const ChatbotWidget: React.FC<ChatbotWidgetProps> = ({ className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentView, setCurrentView] = useState<ChatView>("home");
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    // Auto-hide welcome tooltip after 5 seconds
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const predefinedResponses: {
    [key: string]: { text: string; options?: string[] };
  } = {
    "technical support": {
      text: "I can help you with technical issues. What specific problem are you experiencing?",
      options: [
        "Login Issues",
        "Platform Errors",
        "Performance Problems",
        "Feature Questions",
      ],
    },
    "account questions": {
      text: "I'm here to help with account-related questions. What would you like to know?",
      options: [
        "Profile Settings",
        "Subscription",
        "Billing",
        "Account Security",
      ],
    },
    "general information": {
      text: "I can provide general information about GTI. What would you like to learn about?",
      options: [
        "About GTI",
        "Services",
        "Getting Started",
        "Contact Information",
      ],
    },
    "contact support": {
      text: "You can reach our support <NAME_EMAIL> or use the contact form on this page. Is there anything specific I can help you with right now?",
    },
    "login issues": {
      text: "For login issues, please try:\n• Clear your browser cache and cookies\n• Reset your password using the 'Forgot Password' link\n• Check your internet connection\n• Try using a different browser\n\nIf the problem persists, contact our support <NAME_EMAIL>",
    },
    "platform errors": {
      text: "For platform errors, please:\n• Refresh the page and try again\n• Check if you're using a supported browser\n• Disable browser extensions temporarily\n• Clear your browser cache\n\nIf the error continues, please contact support with details about the error message.",
    },
    "performance problems": {
      text: "To improve platform performance:\n• Check your internet connection speed\n• Close unnecessary browser tabs\n• Clear browser cache and cookies\n• Try using a different browser\n• Ensure your browser is up to date\n\nFor persistent issues, contact our technical team.",
    },
    "about gti": {
      text: "GTI (Global Technology Internationalisation) is a platform that connects technology innovators worldwide. We facilitate international collaboration, business opportunities, and knowledge sharing in the tech sector.\n\nKey features:\n• Technology showcase and discovery\n• International business connections\n• Innovation opportunities\n• Expert support and guidance",
    },
    services: {
      text: "GTI offers comprehensive services including:\n• Technology Internationalisation Support\n• Business Matching and Networking\n• Market Entry Assistance\n• Innovation Consulting\n• Premium Services for Enhanced Visibility\n\nWould you like to know more about any specific service?",
    },
    "getting started": {
      text: "Getting started with GTI is easy:\n1. Create your account and complete your profile\n2. Explore technologies and opportunities\n3. Connect with relevant partners\n4. Utilize our helpdesk for support\n\nNeed help with any of these steps?",
    },
    "profile settings": {
      text: "To manage your profile settings:\n• Go to Profile → Personal/Company sections\n• Update your information and preferences\n• Add your technologies and expertise\n• Set your visibility preferences\n\nNeed specific help with profile setup?",
    },
    hello: {
      text: "Hello! Welcome to GTI Helpdesk. I'm here to assist you with any questions about our platform, technical issues, or general information. How can I help you today?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    hi: {
      text: "Hi there! I'm your GTI assistant. I can help you with technical support, account questions, or provide information about our services. What would you like to know?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
    help: {
      text: "I'm here to help! I can assist you with:\n• Technical issues and troubleshooting\n• Account and profile questions\n• Information about GTI services\n• Connecting you with our support team\n\nWhat do you need help with?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    default: {
      text: "I understand you're looking for help. Let me connect you with the right resources. You can also contact our support team <NAME_EMAIL>",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
  };

  const generateBotResponse = (
    userMessage: string
  ): { text: string; options?: string[] } => {
    const lowerMessage = userMessage.toLowerCase().trim();

    // Direct keyword matches
    const keywordMatches = [
      { keywords: ["hello", "hi", "hey", "greetings"], response: "hello" },
      { keywords: ["help", "assist", "support"], response: "help" },
      {
        keywords: ["technical", "tech", "bug", "error", "problem", "issue"],
        response: "technical support",
      },
      {
        keywords: ["account", "profile", "user", "settings"],
        response: "account questions",
      },
      {
        keywords: ["information", "info", "about", "what is"],
        response: "general information",
      },
      {
        keywords: ["contact", "reach", "email", "phone"],
        response: "contact support",
      },
      {
        keywords: ["login", "sign in", "password", "authentication"],
        response: "login issues",
      },
      {
        keywords: ["slow", "performance", "speed", "loading"],
        response: "performance problems",
      },
      { keywords: ["gti", "platform", "company"], response: "about gti" },
      { keywords: ["service", "offer", "feature"], response: "services" },
      {
        keywords: ["start", "begin", "new", "getting started"],
        response: "getting started",
      },
      {
        keywords: ["profile", "setting", "update"],
        response: "profile settings",
      },
    ];

    // Check for keyword matches
    for (const match of keywordMatches) {
      if (match.keywords.some((keyword) => lowerMessage.includes(keyword))) {
        return predefinedResponses[match.response];
      }
    }

    // Check for exact phrase matches
    for (const [key, response] of Object.entries(predefinedResponses)) {
      if (key !== "default" && lowerMessage.includes(key.toLowerCase())) {
        return response;
      }
    }

    return predefinedResponses.default;
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    // Switch to chat view when sending a message
    setCurrentView("chat");

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isBot: false,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsTyping(true);

    // Simulate bot typing delay
    setTimeout(() => {
      const botResponse = generateBotResponse(text);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: botResponse.text,
        isBot: true,
        timestamp: new Date(),
        options: botResponse.options,
      };

      setMessages((prev) => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds
  };

  const handleNavigation = (view: ChatView) => {
    setCurrentView(view);
    if (view === "chat" && messages.length === 0) {
      // Start with a welcome message when entering chat
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        text: "Hi there! You're speaking with an AI Agent.\nI'm here to answer your questions.\n\nPlease provide context on your project/use-case so I can assist best.",
        isBot: true,
        timestamp: new Date(),
      };
      setMessages([welcomeMessage]);
    }
  };

  const handleOptionClick = (option: string) => {
    handleSendMessage(option);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(inputValue);
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setShowWelcome(false);
    }
  };

  return (
    <div
      className={`fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-[9999] ${className}`}
    >
      {/* Chat Window - Enhanced DataStax Style */}
      {isOpen && (
        <div className="mb-4 w-96 max-w-[calc(100vw-2rem)] h-[38rem] max-h-[calc(100vh-6rem)] bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 rounded-3xl shadow-2xl border border-purple-400/20 flex flex-col animate-fade-in-up overflow-hidden backdrop-blur-xl">
          {/* Enhanced Header */}
          <div className="p-6 pb-4 relative bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="text-white font-bold text-xl tracking-wider drop-shadow-lg">
                  GTI
                </div>
                <div className="w-10 h-10 bg-gradient-to-br from-white/20 to-white/10 rounded-full overflow-hidden ring-2 ring-white/20 shadow-lg">
                  <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                    <span className="text-white text-sm font-bold drop-shadow">
                      G
                    </span>
                  </div>
                </div>
              </div>
              <button
                onClick={toggleChat}
                className="text-white/70 hover:text-white transition-all duration-200 w-8 h-8 flex items-center justify-center rounded-full hover:bg-white/10"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Enhanced Welcome Message */}
            <div className="mt-8">
              <h2 className="text-white text-2xl font-bold mb-3 drop-shadow-lg">
                Hi there! 👋
              </h2>
              <p className="text-white/95 text-lg font-medium drop-shadow">
                How can we help you today?
              </p>
            </div>
          </div>

          {/* Main Content - View Switcher */}
          <div className="flex-1 overflow-hidden">
            {currentView === "home" && (
              <div className="px-6 pb-6 space-y-4">
                {/* Enhanced Ask a Question Card */}
                <button
                  onClick={() =>
                    handleSendMessage("I need help with something")
                  }
                  className="group w-full bg-white/95 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:bg-white transition-all duration-300 hover:shadow-xl hover:scale-[1.02] border border-white/20 hover:border-purple-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="text-left">
                      <h3 className="font-bold text-gray-800 text-base group-hover:text-purple-700 transition-colors">
                        Ask a question
                      </h3>
                      <p className="text-gray-600 text-sm mt-2 group-hover:text-gray-700 transition-colors">
                        AI Agent and team can help
                      </p>
                    </div>
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center group-hover:from-purple-200 group-hover:to-purple-300 transition-all duration-300 shadow-md">
                      <svg
                        className="w-5 h-5 text-purple-600 group-hover:text-purple-700 transition-colors"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </button>

                {/* Enhanced Featured Content Card */}
                <div className="group bg-gradient-to-r from-GTI-BLUE-default to-purple-700 rounded-2xl p-5 text-white relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border border-blue-400/20 hover:border-blue-300/30">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10">
                    <div className="text-sm text-blue-200 font-bold mb-3 tracking-wide">
                      🎯 UPCOMING WEBINAR
                    </div>
                    <h3 className="font-bold text-base mb-3 leading-tight group-hover:text-blue-100 transition-colors">
                      Global Technology Internationalisation: Expanding Your
                      Tech Business
                    </h3>
                    <div className="text-sm text-blue-100 font-medium">
                      with GTI Expert Team
                    </div>
                    <div className="mt-3 text-xs text-blue-200 opacity-80">
                      Click to learn more →
                    </div>
                  </div>
                  <div className="absolute right-3 top-3 w-14 h-14 bg-gradient-to-br from-blue-200/80 to-blue-300/60 rounded-full overflow-hidden shadow-lg ring-2 ring-white/20 group-hover:scale-110 transition-transform duration-300">
                    <div className="w-full h-full bg-gradient-to-br from-blue-300 to-blue-500 flex items-center justify-center">
                      <span className="text-white text-sm font-bold drop-shadow">
                        GTI
                      </span>
                    </div>
                  </div>
                </div>

                {/* Enhanced Support Card */}
                <button
                  onClick={() =>
                    handleSendMessage("I need support with my account")
                  }
                  className="group w-full bg-white/95 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] border border-white/20 hover:border-green-200"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 text-left">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="text-sm text-green-600 font-bold tracking-wide">
                          💬 SUPPORT
                        </div>
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      </div>
                      <h3 className="font-bold text-gray-800 text-base mb-2 leading-tight group-hover:text-green-700 transition-colors">
                        Need help with your GTI account or platform features?
                      </h3>
                      <p className="text-gray-600 text-sm group-hover:text-gray-700 transition-colors">
                        Contact our support team | Available 24/7
                      </p>
                    </div>
                    <div className="w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center group-hover:from-green-200 group-hover:to-green-300 transition-all duration-300 shadow-md">
                      <svg
                        className="w-5 h-5 text-green-600 group-hover:text-green-700 transition-colors"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"
                        />
                      </svg>
                    </div>
                  </div>
                </button>

                {/* Enhanced Messages Area */}
                {messages.length > 0 && (
                  <div className="space-y-4 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          message.isBot ? "justify-start" : "justify-end"
                        } animate-fade-in-up`}
                      >
                        <div
                          className={`max-w-xs px-5 py-4 rounded-2xl shadow-lg ${
                            message.isBot
                              ? "bg-white/95 backdrop-blur-sm text-gray-800 border border-white/20"
                              : "bg-gradient-to-r from-white/20 to-white/30 text-white border border-white/20"
                          }`}
                        >
                          <div className="text-sm whitespace-pre-line font-medium leading-relaxed">
                            {message.text}
                          </div>
                          {message.options && (
                            <div className="mt-4 space-y-2">
                              {message.options.map((option, index) => (
                                <button
                                  key={index}
                                  onClick={() => handleOptionClick(option)}
                                  className="block w-full text-left px-4 py-3 text-sm bg-white/90 border border-gray-200 rounded-xl hover:bg-white hover:shadow-md transition-all duration-200 text-gray-700 font-medium hover:border-purple-300"
                                >
                                  {option}
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-white/95 px-4 py-2 rounded-2xl">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}

            {/* Messages View */}
            {currentView === "messages" && (
              <div className="flex flex-col h-full">
                <div className="p-6 pb-4 border-b border-white/10">
                  <h2 className="text-white text-xl font-bold">Messages</h2>
                </div>
                <div className="flex-1 px-6 py-4 space-y-4">
                  <div className="flex items-center space-x-3 p-4 bg-white/95 rounded-2xl hover:bg-white transition-colors cursor-pointer">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">AI</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-gray-800">
                          Hey there! 👋 Do you have any questions?
                        </h3>
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                      <p className="text-gray-500 text-sm">
                        GTI Assistant • Just now
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-4 bg-white/95 rounded-2xl hover:bg-white transition-colors cursor-pointer">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">S</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-gray-800">
                          Support Team Available
                        </h3>
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                      <p className="text-gray-500 text-sm">
                        GTI Support • 5m ago
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <button
                    onClick={() => handleNavigation("chat")}
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-3 px-6 rounded-2xl font-semibold hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg"
                  >
                    Ask a question
                  </button>
                </div>
              </div>
            )}

            {/* News View */}
            {currentView === "news" && (
              <div className="flex flex-col h-full">
                <div className="p-6 pb-4 border-b border-white/10">
                  <div className="flex items-center justify-between">
                    <h2 className="text-white text-xl font-bold">News</h2>
                    <div className="w-10 h-10 bg-gradient-to-br from-white/20 to-white/10 rounded-full overflow-hidden ring-2 ring-white/20">
                      <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                        <span className="text-white text-sm font-bold">G</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-white/80 text-sm mt-2">
                    Latest from Team GTI
                  </p>
                </div>

                <div className="flex-1 px-6 py-4 space-y-4">
                  {/* Featured News Card */}
                  <div className="bg-gradient-to-r from-GTI-BLUE-default to-purple-700 rounded-2xl p-5 text-white relative overflow-hidden">
                    <div className="text-sm text-blue-200 font-bold mb-3">
                      🎯 UPCOMING WEBINAR
                    </div>
                    <h3 className="font-bold text-base mb-3 leading-tight">
                      Global Technology Internationalisation: Expanding Your
                      Tech Business
                    </h3>
                    <div className="text-sm text-blue-100 font-medium">
                      with GTI Expert Team
                    </div>
                  </div>

                  {/* Event Card */}
                  <div className="bg-white/95 rounded-2xl p-4">
                    <div className="text-sm text-purple-600 font-bold mb-2">
                      Event
                    </div>
                    <h3 className="font-bold text-gray-800 text-base mb-2">
                      Build Real-Time GenAI Product Recs that Boost Cart Size
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Tuesday, July 22 | Livestream
                    </p>
                    <div className="flex items-center justify-between mt-3">
                      <span className="text-xs text-gray-500">
                        Click to learn more
                      </span>
                      <svg
                        className="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </div>

                  {/* All caught up message */}
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-white/60"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <p className="text-white/60 font-medium">
                      You're all caught up!
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Chat View */}
            {currentView === "chat" && (
              <div className="flex flex-col h-full">
                <div className="p-6 pb-4 border-b border-white/10">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setCurrentView("home")}
                      className="text-white/70 hover:text-white transition-colors"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                    </button>
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">GTI</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">GTI Bot</h3>
                      <p className="text-white/70 text-sm">
                        The team can also help
                      </p>
                    </div>
                  </div>
                </div>

                {/* Chat Messages */}
                <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.isBot ? "justify-start" : "justify-end"
                      } animate-fade-in-up`}
                    >
                      <div
                        className={`max-w-xs px-5 py-4 rounded-2xl ${
                          message.isBot
                            ? "bg-gray-100 text-gray-800"
                            : "bg-gradient-to-r from-purple-600 to-purple-700 text-white"
                        }`}
                      >
                        <div className="text-sm whitespace-pre-line font-medium leading-relaxed">
                          {message.text}
                        </div>
                        {message.options && (
                          <div className="mt-4 space-y-2">
                            {message.options.map((option, index) => (
                              <button
                                key={index}
                                onClick={() => handleOptionClick(option)}
                                className="block w-full text-left px-4 py-3 text-sm bg-white border border-gray-200 rounded-xl hover:bg-gray-50 hover:shadow-md transition-all duration-200 text-gray-700 font-medium hover:border-purple-300"
                              >
                                {option}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}

                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="bg-gray-100 px-5 py-4 rounded-2xl">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                          <div
                            className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.1s" }}
                          ></div>
                          <div
                            className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.2s" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Chat Input */}
                <div className="p-6 border-t border-white/10">
                  <form onSubmit={handleSubmit} className="flex space-x-3">
                    <div className="flex-1 relative">
                      <input
                        ref={inputRef}
                        type="text"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder="Ask a question..."
                        className="w-full px-4 py-3 bg-white/95 border border-purple-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent text-sm font-medium"
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-2">
                        <button
                          type="button"
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        </button>
                        <button
                          type="button"
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <button
                      type="submit"
                      disabled={!inputValue.trim()}
                      className="px-4 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-2xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                    </button>
                  </form>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Bottom Navigation */}
          {currentView !== "chat" && (
            <div className="bg-white/95 backdrop-blur-sm rounded-b-3xl p-5 border-t border-white/20 shadow-lg">
              <div className="flex items-center justify-around">
                <button
                  onClick={() => handleNavigation("home")}
                  className={`group flex flex-col items-center space-y-2 transition-all duration-200 hover:scale-110 ${
                    currentView === "home"
                      ? "text-purple-600"
                      : "text-gray-400 hover:text-gray-600"
                  }`}
                >
                  <div
                    className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-200 shadow-md ${
                      currentView === "home"
                        ? "bg-gradient-to-br from-purple-100 to-purple-200 group-hover:from-purple-200 group-hover:to-purple-300"
                        : "bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-gray-200 group-hover:to-gray-300"
                    }`}
                  >
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                  </div>
                  <span
                    className={`text-xs ${
                      currentView === "home" ? "font-bold" : "font-medium"
                    }`}
                  >
                    Home
                  </span>
                </button>

                <button
                  onClick={() => handleNavigation("messages")}
                  className={`group flex flex-col items-center space-y-2 transition-all duration-200 hover:scale-110 ${
                    currentView === "messages"
                      ? "text-purple-600"
                      : "text-gray-400 hover:text-gray-600"
                  }`}
                >
                  <div
                    className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-200 shadow-md ${
                      currentView === "messages"
                        ? "bg-gradient-to-br from-purple-100 to-purple-200 group-hover:from-purple-200 group-hover:to-purple-300"
                        : "bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-gray-200 group-hover:to-gray-300"
                    }`}
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      />
                    </svg>
                  </div>
                  <span
                    className={`text-xs ${
                      currentView === "messages" ? "font-bold" : "font-medium"
                    }`}
                  >
                    Messages
                  </span>
                </button>

                <button
                  onClick={() => handleNavigation("news")}
                  className={`group flex flex-col items-center space-y-2 transition-all duration-200 hover:scale-110 ${
                    currentView === "news"
                      ? "text-purple-600"
                      : "text-gray-400 hover:text-gray-600"
                  }`}
                >
                  <div
                    className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-200 shadow-md ${
                      currentView === "news"
                        ? "bg-gradient-to-br from-purple-100 to-purple-200 group-hover:from-purple-200 group-hover:to-purple-300"
                        : "bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-gray-200 group-hover:to-gray-300"
                    }`}
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 17h5l-5 5v-5zM12 17H7a2 2 0 01-2-2V5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <span
                    className={`text-xs ${
                      currentView === "news" ? "font-bold" : "font-medium"
                    }`}
                  >
                    News
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Enhanced Chat Toggle Button */}
      <button
        onClick={toggleChat}
        className="w-16 h-16 bg-gradient-to-br from-GTI-BLUE-default via-purple-600 to-purple-700 text-white rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center group relative ring-4 ring-white/20 hover:ring-white/30"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-white/5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        {isOpen ? (
          <svg
            className="w-7 h-7 relative z-10 drop-shadow-lg"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2.5}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ) : (
          <svg
            className="w-7 h-7 relative z-10 drop-shadow-lg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </button>

      {/* Enhanced Welcome Tooltip */}
      {showWelcome && !isOpen && (
        <div className="absolute bottom-20 right-0 bg-gradient-to-r from-white to-gray-50 rounded-2xl shadow-2xl p-4 max-w-xs border border-purple-200 animate-fade-in-up backdrop-blur-sm">
          <div className="text-sm text-gray-800 font-semibold mb-1">
            👋 Need help? I'm here to assist you!
          </div>
          <div className="text-xs text-gray-600">
            Click to start a conversation with our AI assistant
          </div>
          <div className="absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-3 h-3 bg-white border-r border-b border-purple-200 shadow-sm"></div>
          <button
            onClick={() => setShowWelcome(false)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 transition-colors w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default ChatbotWidget;
