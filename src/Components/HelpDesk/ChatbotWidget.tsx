import React, { useState, useRef, useEffect } from "react";

interface Message {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  options?: string[];
}

interface ChatbotWidgetProps {
  className?: string;
}

const ChatbotWidget: React.FC<ChatbotWidgetProps> = ({ className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      text: "👋 Hi! I'm your GTI Helpdesk assistant. I'm here to help you with any questions about our platform, technical issues, or general information.\n\nHow can I assist you today?",
      isBot: true,
      timestamp: new Date(),
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    // Auto-hide welcome tooltip after 5 seconds
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const predefinedResponses: {
    [key: string]: { text: string; options?: string[] };
  } = {
    "technical support": {
      text: "I can help you with technical issues. What specific problem are you experiencing?",
      options: [
        "Login Issues",
        "Platform Errors",
        "Performance Problems",
        "Feature Questions",
      ],
    },
    "account questions": {
      text: "I'm here to help with account-related questions. What would you like to know?",
      options: [
        "Profile Settings",
        "Subscription",
        "Billing",
        "Account Security",
      ],
    },
    "general information": {
      text: "I can provide general information about GTI. What would you like to learn about?",
      options: [
        "About GTI",
        "Services",
        "Getting Started",
        "Contact Information",
      ],
    },
    "contact support": {
      text: "You can reach our support <NAME_EMAIL> or use the contact form on this page. Is there anything specific I can help you with right now?",
    },
    "login issues": {
      text: "For login issues, please try:\n• Clear your browser cache and cookies\n• Reset your password using the 'Forgot Password' link\n• Check your internet connection\n• Try using a different browser\n\nIf the problem persists, contact our support <NAME_EMAIL>",
    },
    "platform errors": {
      text: "For platform errors, please:\n• Refresh the page and try again\n• Check if you're using a supported browser\n• Disable browser extensions temporarily\n• Clear your browser cache\n\nIf the error continues, please contact support with details about the error message.",
    },
    "performance problems": {
      text: "To improve platform performance:\n• Check your internet connection speed\n• Close unnecessary browser tabs\n• Clear browser cache and cookies\n• Try using a different browser\n• Ensure your browser is up to date\n\nFor persistent issues, contact our technical team.",
    },
    "about gti": {
      text: "GTI (Global Technology Internationalisation) is a platform that connects technology innovators worldwide. We facilitate international collaboration, business opportunities, and knowledge sharing in the tech sector.\n\nKey features:\n• Technology showcase and discovery\n• International business connections\n• Innovation opportunities\n• Expert support and guidance",
    },
    services: {
      text: "GTI offers comprehensive services including:\n• Technology Internationalisation Support\n• Business Matching and Networking\n• Market Entry Assistance\n• Innovation Consulting\n• Premium Services for Enhanced Visibility\n\nWould you like to know more about any specific service?",
    },
    "getting started": {
      text: "Getting started with GTI is easy:\n1. Create your account and complete your profile\n2. Explore technologies and opportunities\n3. Connect with relevant partners\n4. Utilize our helpdesk for support\n\nNeed help with any of these steps?",
    },
    "profile settings": {
      text: "To manage your profile settings:\n• Go to Profile → Personal/Company sections\n• Update your information and preferences\n• Add your technologies and expertise\n• Set your visibility preferences\n\nNeed specific help with profile setup?",
    },
    hello: {
      text: "Hello! Welcome to GTI Helpdesk. I'm here to assist you with any questions about our platform, technical issues, or general information. How can I help you today?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    hi: {
      text: "Hi there! I'm your GTI assistant. I can help you with technical support, account questions, or provide information about our services. What would you like to know?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
    help: {
      text: "I'm here to help! I can assist you with:\n• Technical issues and troubleshooting\n• Account and profile questions\n• Information about GTI services\n• Connecting you with our support team\n\nWhat do you need help with?",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
        "Contact Support",
      ],
    },
    default: {
      text: "I understand you're looking for help. Let me connect you with the right resources. You can also contact our support team <NAME_EMAIL>",
      options: [
        "Technical Support",
        "Account Questions",
        "General Information",
      ],
    },
  };

  const generateBotResponse = (
    userMessage: string
  ): { text: string; options?: string[] } => {
    const lowerMessage = userMessage.toLowerCase().trim();

    // Direct keyword matches
    const keywordMatches = [
      { keywords: ["hello", "hi", "hey", "greetings"], response: "hello" },
      { keywords: ["help", "assist", "support"], response: "help" },
      {
        keywords: ["technical", "tech", "bug", "error", "problem", "issue"],
        response: "technical support",
      },
      {
        keywords: ["account", "profile", "user", "settings"],
        response: "account questions",
      },
      {
        keywords: ["information", "info", "about", "what is"],
        response: "general information",
      },
      {
        keywords: ["contact", "reach", "email", "phone"],
        response: "contact support",
      },
      {
        keywords: ["login", "sign in", "password", "authentication"],
        response: "login issues",
      },
      {
        keywords: ["slow", "performance", "speed", "loading"],
        response: "performance problems",
      },
      { keywords: ["gti", "platform", "company"], response: "about gti" },
      { keywords: ["service", "offer", "feature"], response: "services" },
      {
        keywords: ["start", "begin", "new", "getting started"],
        response: "getting started",
      },
      {
        keywords: ["profile", "setting", "update"],
        response: "profile settings",
      },
    ];

    // Check for keyword matches
    for (const match of keywordMatches) {
      if (match.keywords.some((keyword) => lowerMessage.includes(keyword))) {
        return predefinedResponses[match.response];
      }
    }

    // Check for exact phrase matches
    for (const [key, response] of Object.entries(predefinedResponses)) {
      if (key !== "default" && lowerMessage.includes(key.toLowerCase())) {
        return response;
      }
    }

    return predefinedResponses.default;
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isBot: false,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsTyping(true);

    // Simulate bot typing delay
    setTimeout(() => {
      const botResponse = generateBotResponse(text);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: botResponse.text,
        isBot: true,
        timestamp: new Date(),
        options: botResponse.options,
      };

      setMessages((prev) => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds
  };

  const handleOptionClick = (option: string) => {
    handleSendMessage(option);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(inputValue);
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setShowWelcome(false);
    }
  };

  return (
    <div
      className={`fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-[9999] ${className}`}
    >
      {/* Chat Window */}
      {isOpen && (
        <div className="mb-4 w-80 max-w-[calc(100vw-2rem)] h-96 max-h-[calc(100vh-8rem)] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col animate-fade-in-up">
          {/* Header */}
          <div className="bg-gradient-to-r from-GTI-BLUE-default to-purple-600 text-white p-4 rounded-t-2xl flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-sm">GTI Assistant</h3>
                <p className="text-xs text-white/80">Online now</p>
              </div>
            </div>
            <button
              onClick={toggleChat}
              className="text-white/80 hover:text-white transition-colors"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.isBot ? "justify-start" : "justify-end"
                }`}
              >
                <div
                  className={`max-w-xs px-4 py-3 rounded-2xl ${
                    message.isBot
                      ? "bg-gray-100 text-gray-800"
                      : "bg-gradient-to-r from-GTI-BLUE-default to-purple-600 text-white"
                  }`}
                >
                  <div className="text-sm whitespace-pre-line">
                    {message.text}
                  </div>
                  {message.options && (
                    <div className="mt-3 space-y-2">
                      {message.options.map((option, index) => (
                        <button
                          key={index}
                          onClick={() => handleOptionClick(option)}
                          className="block w-full text-left px-3 py-2 text-xs bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-GTI-BLUE-default/30 transition-all duration-200 text-gray-700 font-medium"
                        >
                          {option}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 px-4 py-2 rounded-2xl">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200">
            <form onSubmit={handleSubmit} className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-transparent text-sm"
              />
              <button
                type="submit"
                disabled={!inputValue.trim()}
                className="px-4 py-2 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 text-white rounded-full hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  />
                </svg>
              </button>
            </form>
          </div>
        </div>
      )}

      {/* Chat Toggle Button */}
      <button
        onClick={toggleChat}
        className="w-14 h-14 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center group relative"
      >
        {isOpen ? (
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ) : (
          <>
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                clipRule="evenodd"
              />
            </svg>
            {/* Notification dot */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            {/* Ripple effect */}
            <div className="absolute inset-0 rounded-full bg-white/20 animate-ping opacity-75"></div>
          </>
        )}
      </button>

      {/* Welcome Tooltip */}
      {showWelcome && !isOpen && (
        <div className="absolute bottom-16 right-0 bg-white rounded-lg shadow-lg p-3 max-w-xs border border-gray-200 animate-fade-in-up">
          <div className="text-sm text-gray-800 font-medium">
            👋 Need help? I'm here to assist you!
          </div>
          <div className="absolute bottom-0 right-4 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200"></div>
          <button
            onClick={() => setShowWelcome(false)}
            className="absolute top-1 right-1 text-gray-400 hover:text-gray-600"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default ChatbotWidget;
